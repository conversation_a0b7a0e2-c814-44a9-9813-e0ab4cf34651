<?php
use yii\helpers\Html;
use yii\widgets\ActiveForm;
?>

<div class="card">
    <div class="card-header">
        <h3 class="card-title">Топ сотувчилар</h3>
    </div>
    <div class="card-body">
        <div class="mb-3 w-100 d-flex justify-content-end" style="max-width: 1460px; margin: 0 auto;">
            <?php $form = ActiveForm::begin(['method' => 'post', 'id' => 'sellersFilterForm']); ?>
            <div class="row compact-form d-flex align-items-center" style="width: auto;">
                <div class="col-auto form-item">
                    <?= Html::label('Бошланиш санаси', 'start_date', ['class' => 'form-label']) ?>
                    <?= Html::input('text', 'start_date', $startDate, [
                        'class' => 'form-control flatpickr form-control-sm uniform-height',
                        'placeholder' => 'Выберите дату',
                        'autocomplete' => 'off'
                    ]) ?>
                </div>
                <div class="col-auto form-item">
                    <?= Html::label('Тугаш санаси', 'end_date', ['class' => 'form-label']) ?>
                    <?= Html::input('text', 'end_date', $endDate, [
                        'class' => 'form-control flatpickr form-control-sm uniform-height',
                        'placeholder' => 'Выберите дату',
                        'autocomplete' => 'off'
                    ]) ?>
                </div>
                <div class="col-auto">
                    <label class="form-label d-block">&nbsp;</label>
                    <?= Html::submitButton('Излаш', ['class' => 'btn btn-primary btn-sm uniform-height']) ?>
                </div>
            </div>
            <?php ActiveForm::end(); ?>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div id="topSellersSalesChart" style="height: 400px;"></div>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-md-12">
                <div id="topSellersQuantityChart" style="height: 400px;"></div>
            </div>
        </div>
    </div>
</div>

<?php
$this->registerCssFile('https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css');
$this->registerJsFile('https://cdn.jsdelivr.net/npm/flatpickr', ['depends' => [\yii\web\JqueryAsset::class]]);
$this->registerJsFile('https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ru.js', ['depends' => [\yii\web\JqueryAsset::class]]);

$this->registerJs("
$(document).ready(function() {
    // Инициализация календаря
    flatpickr('.flatpickr', {
        locale: 'ru',
        dateFormat: 'Y-m-d',
        allowInput: true
    });

    var colors = [
        '#008FFB', '#00E396', '#FEB019', '#FF4560', '#775DD0',
        '#3F51B5', '#03A9F4', '#4CAF50', '#F9CE1D', '#FF9800',
        '#9C27B0', '#673AB7', '#E91E63', '#795548', '#607D8B'
    ];

    function initTopSellersChart() {
        if (!window.chartData.topSellers ||
            !window.chartData.topSellers.bySales ||
            !window.chartData.topSellers.byQuantity) {
            return;
        }

        // График по сумме продаж (ограничиваем топ-15 продавцов)
        var topSalesData = window.chartData.topSellers.bySales.slice(0, 15);
        var salesOptions = {
            series: [{
                name: 'Сумма продаж',
                data: topSalesData.map(item => ({
                    x: item.seller_name + ' (' + item.date_group + ')',
                    y: parseFloat(item.total_sales_amount)
                }))
            }],
            chart: {
                type: 'bar',
                height: 450, // Фиксированная высота
                zoom: { enabled: true },
                toolbar: { show: true }
            },
            plotOptions: {
                bar: {
                    horizontal: false,
                    dataLabels: { position: 'top' },
                    columnWidth: '60%',
                    distributed: true
                }
            },
            colors: colors,
            dataLabels: {
                enabled: true,
                formatter: function(val) {
                    return new Intl.NumberFormat('ru-RU', {
                        maximumFractionDigits: 0
                    }).format(val);
                },
                offsetY: -20,
                style: {
                    fontSize: '12px',
                    colors: ['#304758']
                }
            },
            legend: { show: false },
            xaxis: {
                type: 'category',
                labels: {
                    rotate: -45,
                    style: { fontSize: '12px' }
                },
                tickPlacement: 'on'
            },
            yaxis: {
                title: { text: 'Сотув суммаси' },
                labels: {
                    formatter: function(val) {
                        return new Intl.NumberFormat('ru-RU', {
                            maximumFractionDigits: 0
                        }).format(val);
                    }
                }
            },
            title: {
                text: 'Топ-15 сотувчилар (савдо миқдори бўйича)',
                align: 'center'
            }
        };

        // График по площади продаж (ограничиваем топ-15 продавцов)
        // Отладочный вывод
        console.log('TopSellers byQuantity data:', window.chartData.topSellers.byQuantity);

        var topQuantityData = window.chartData.topSellers.byQuantity.slice(0, 15);
        var quantityOptions = {
            series: [{
                name: 'Сотувлар майдони (м²)',
                data: topQuantityData.map(item => {
                    console.log('Seller item:', item);
                    return {
                        x: item.seller_name + ' (' + item.date_group + ')',
                        y: parseFloat(item.total_meters_sold || 0)
                    };
                })
            }],
            chart: {
                type: 'bar',
                height: 450, // Фиксированная высота
                zoom: { enabled: true },
                toolbar: { show: true }
            },
            plotOptions: {
                bar: {
                    horizontal: false,
                    dataLabels: { position: 'top' },
                    columnWidth: '60%',
                    distributed: true
                }
            },
            colors: colors,
            dataLabels: {
                enabled: true,
                formatter: function(val) {
                    return val.toFixed(2) + ' м²';
                },
                offsetY: -20,
                style: {
                    fontSize: '12px',
                    colors: ['#304758']
                }
            },
            legend: { show: false },
            xaxis: {
                type: 'category',
                labels: {
                    rotate: -45,
                    style: { fontSize: '12px' }
                },
                tickPlacement: 'on'
            },
            yaxis: {
                title: { text: 'Сотув майдони (м²)' },
                labels: {
                    formatter: function(val) {
                        return new Intl.NumberFormat('ru-RU', {
                            maximumFractionDigits: 2
                        }).format(val);
                    }
                }
            },
            title: {
                text: 'Сотув майдони бўйича сотувчилар (м²)',
                align: 'center'
            }
        };

        if (document.querySelector('#topSellersSalesChart')) {
            charts.topSellersSalesChart = new ApexCharts(document.querySelector('#topSellersSalesChart'), salesOptions);
            charts.topSellersSalesChart.render();
        }

        if (document.querySelector('#topSellersQuantityChart')) {
            charts.topSellersQuantityChart = new ApexCharts(document.querySelector('#topSellersQuantityChart'), quantityOptions);
            charts.topSellersQuantityChart.render();
        }
    }

    // Обработчик формы фильтрации
    $('#sellersFilterForm').on('submit', function(e) {
        e.preventDefault();
        var formData = $(this).serializeArray();

        $.ajax({
            url: '" . \yii\helpers\Url::to(['dashboard/get-top-sellers']) . "',
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    updateTopSellersCharts(response.data);
                }
            }
        });
    });

    function updateTopSellersCharts(data) {
        if (data.bySales && charts.topSellersSalesChart) {
            // Ограничиваем топ-15 продавцов при обновлении
            var topSalesData = data.bySales.slice(0, 15);
            charts.topSellersSalesChart.updateSeries([{
                name: 'Сумма продаж',
                data: topSalesData.map(item => ({
                    x: item.seller_name + ' (' + item.date_group + ')',
                    y: parseFloat(item.total_sales_amount)
                }))
            }]);
        }

        if (data.byQuantity && charts.topSellersQuantityChart) {
            console.log('Update TopSellers byQuantity data:', data.byQuantity);
            // Ограничиваем топ-15 продавцов при обновлении
            var topQuantityData = data.byQuantity.slice(0, 15);
            charts.topSellersQuantityChart.updateSeries([{
                name: 'Сотувлар майдони (м²)',
                data: topQuantityData.map(item => {
                    console.log('Update seller item:', item);
                    return {
                        x: item.seller_name + ' (' + item.date_group + ')',
                        y: parseFloat(item.total_meters_sold || 0)
                    };
                })
            }]);
        }
    }

    // Инициализация при загрузке
    initTopSellersChart();
});
");
